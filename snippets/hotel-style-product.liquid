<!-- Hotel Style Product Section -->
{{ 'section-main-product.css' | asset_url | stylesheet_tag }}

<div class="hotel-booking-layout">
  <!-- Hotel Header -->
  <div class="hotel-header">
    <div class="hotel-title-section">
      <h1 class="hotel-title">{{ product.title | escape }}</h1>
      
      <div class="hotel-rating">
        <div class="rating-stars">
          {% for i in (1..5) %}
            <svg class="star" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
          {% endfor %}
        </div>
      </div>
      

      
      <div class="price-match-badge">
        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
        </svg>
        We Price Match
      </div>
    </div>
    
    <div class="price-display-btn" onclick="document.querySelector('.product-form__cart-submit')?.click() || document.querySelector('[name=add]')?.click()">
      <span class="price-amount" style="color:#fff;     -webkit-text-fill-color: #fff !important;">
        {{ product.selected_or_first_available_variant.price | money }}
      </span>
    </div>
  </div>

  <!-- Hotel Gallery -->
  <div class="hotel-gallery-container">
    <div class="hotel-gallery-grid">
      <div class="hotel-main-image" onclick="openImageModal('{{ product.featured_media | image_url: width: 1200 }}', 0)">
        {% if product.featured_media %}
          <img src="{{ product.featured_media | image_url: width: 800 }}"
               alt="{{ product.featured_media.alt | escape }}"
               loading="lazy">
        {% endif %}
        <div class="image-zoom-icon">
          <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <circle cx="11" cy="11" r="8"/>
            <path d="m21 21-4.35-4.35"/>
            <circle cx="11" cy="11" r="3"/>
          </svg>
        </div>
      </div>
      
      <div class="hotel-thumbnails">
        {% assign total_images = product.media | where: 'media_type', 'image' | size %}
        {% assign thumbnail_images = product.media | where: 'media_type', 'image' | slice: 1, 3 %}

        {% for media in thumbnail_images %}
          <div class="hotel-thumbnail" onclick="openImageModal('{{ media | image_url: width: 1200 }}', {{ forloop.index }})">
            <img src="{{ media | image_url: width: 300 }}"
                 alt="{{ media.alt | escape }}"
                 loading="lazy">
          </div>
        {% endfor %}

        {% if total_images > 4 %}
          <div class="hotel-thumbnail view-all-overlay" onclick="openImageModal('{{ product.media[4] | image_url: width: 1200 }}', 5)">
            <img src="{{ product.media[4] | image_url: width: 300 }}"
                 alt="{{ product.media[4].alt | escape }}"
                 loading="lazy">
            <div class="view-all-photos">
              <div class="view-all-text">
                <span class="view-all-label">View all</span>
                <span class="view-all-count">{{ total_images }} Images</span>
              </div>
            </div>
          </div>
        {% else %}
          {% assign fourth_image = product.media | where: 'media_type', 'image' | slice: 3, 1 | first %}
          {% if fourth_image %}
            <div class="hotel-thumbnail" onclick="openImageModal('{{ fourth_image | image_url: width: 1200 }}', 4)">
              <img src="{{ fourth_image | image_url: width: 300 }}"
                   alt="{{ fourth_image.alt | escape }}"
                   loading="lazy">
            </div>
          {% endif %}
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Navigation Tabs -->
  <div class="hotel-nav-tabs">
    <button class="hotel-nav-tab active" data-tab="overview">Overview</button>
  </div>

  <!-- Main Content -->
  <div class="hotel-content">
    <div class="hotel-main-content">
      <!-- Highlights Section -->
      <div class="highlights-section">
        <h3 class="highlights-title">
          <span class="highlights-badge">Highlights</span>
        </h3>
        
        <div class="highlights-grid">
          <div class="highlight-item">
            <div class="highlight-icon-container">
              <svg class="highlight-icon" style="color:#fff !important;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
              </svg>
              <svg class="highlight-icon-accent" fill="currentColor" viewBox="0 0 24 24">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
            </div>
            <span class="highlight-text">Low-rise Building</span>
          </div>

          <div class="highlight-item">
            <div class="highlight-icon-container parking-icon">
              <span class="parking-letter">P</span>
            </div>
            <span class="highlight-text">Free parking</span>
          </div>

          <div class="highlight-item">
            <div class="highlight-icon-container">
              <svg class="highlight-icon" style="color:#fff !important; " fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
              </svg>
              <svg class="highlight-icon-accent" fill="currentColor" viewBox="0 0 24 24">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
            </div>
            <span class="highlight-text">Delicious breakfast</span>
          </div>

          <div class="highlight-item">
            <div class="highlight-icon-container">
              <svg class="highlight-icon" style="color:#fff !important; " fill="currentColor" viewBox="0 0 24 24">
                <rect x="3" y="3" width="7" height="7" rx="1"/>
                <rect x="14" y="3" width="7" height="7" rx="1"/>
                <rect x="14" y="14" width="7" height="7" rx="1"/>
                <rect x="3" y="14" width="7" height="7" rx="1"/>
              </svg>
            </div>
            <span class="highlight-text">Lots to do</span>
          </div>
        </div>
      </div>

      <!-- Amenities Section -->
      <div class="amenities-section">
        <h3 class="amenities-title">Amenities</h3>

        <div class="amenities-grid">
          <div class="amenity-item">
            <svg class="amenity-icon" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
            </svg>
            <span class="amenity-text">Outdoor swimming pool</span>
          </div>

          <div class="amenity-item">
            <svg class="amenity-icon" fill="currentColor" viewBox="0 0 20 20">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
            </svg>
            <span class="amenity-text">Sauna</span>
          </div>

          <div class="amenity-item">
            <svg class="amenity-icon" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 1a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm4-4a1 1 0 100 2h.01a1 1 0 100-2H13zM9 9a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zM7 8a1 1 0 000 2h.01a1 1 0 000-2H7z" clip-rule="evenodd"/>
            </svg>
            <span class="amenity-text">Gym</span>
          </div>

          <div class="amenity-item">
            <svg class="amenity-icon" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z" clip-rule="evenodd"/>
            </svg>
            <span class="amenity-text">Bar</span>
          </div>

          <div class="amenity-item">
            <svg class="amenity-icon" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 1a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm4-4a1 1 0 100 2h.01a1 1 0 100-2H13zM9 9a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zM7 8a1 1 0 000 2h.01a1 1 0 000-2H7z" clip-rule="evenodd"/>
            </svg>
            <span class="amenity-text">Restaurant</span>
          </div>

          <div class="amenity-item">
            <svg class="amenity-icon" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"/>
              <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z"/>
            </svg>
            <span class="amenity-text">Taxi booking service</span>
          </div>

          <div class="amenity-item">
            <svg class="amenity-icon" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"/>
              <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z"/>
            </svg>
            <span class="amenity-text">Public parking <span style="color: #059669; font-weight: 500;">Free</span></span>
          </div>

          <div class="amenity-item">
            <svg class="amenity-icon" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm12 12H4V8h12v8z" clip-rule="evenodd"/>
            </svg>
            <span class="amenity-text">Conference room</span>
          </div>
        </div>

        <a href="#amenities" class="amenities-link">All amenities</a>
      </div>


    </div>

    <!-- Sidebar -->
    <div class="hotel-sidebar">
      <!-- Booking Form -->
      <div class="booking-form-section">
        <div class="booking-form-header">
          <span class="from-label">FROM</span>
          <div class="price-display" id="totalPrice">
            {{ product.selected_or_first_available_variant.price | money }} GEL
          </div>
        </div>

        <form class="booking-form" id="bookingForm">
          <!-- Date Field -->
          <div class="form-group">
            <label for="booking-date">Date</label>
            <input type="date" id="booking-date" name="booking-date" class="form-input" required>
          </div>

          <!-- Adults and Children Fields -->
          <div class="hotel-form-row">
            <div class="hotel-form-group">
              <label for="adults">Adults</label>
              <div class="hotel-quantity-selector">
                <button type="button" class="hotel-qty-btn hotel-minus" data-target="adults">-</button>
                <input type="number" id="adults" name="adults" value="2" min="1" max="4" class="hotel-qty-input" readonly>
                <button type="button" class="hotel-qty-btn hotel-plus" data-target="adults">+</button>
              </div>
              <small class="hotel-age-note">Age 18+</small>
            </div>

            <div class="hotel-form-group">
              <label for="children">Children</label>
              <div class="hotel-quantity-selector">
                <button type="button" class="hotel-qty-btn hotel-minus" data-target="children">-</button>
                <input type="number" id="children" name="children" value="0" min="0" max="2" class="hotel-qty-input" readonly>
                <button type="button" class="hotel-qty-btn hotel-plus" data-target="children">+</button>
              </div>
              <small class="hotel-age-note">Age 6-17</small>
            </div>
          </div>

          <!-- Guest Names -->
          <div class="form-group">
            <label>Guest names *</label>
            <div class="guest-names" id="guestNames">
              <div class="guest-name-row">
                <select class="title-select">
                  <option>Mr</option>
                  <option>Ms</option>
                  <option>Mrs</option>
                </select>
                <input type="text" placeholder="Guest name" class="guest-input" required>
              </div>
              <div class="guest-name-row">
                <select class="title-select">
                  <option>Mr</option>
                  <option>Ms</option>
                  <option>Mrs</option>
                </select>
                <input type="text" placeholder="Guest name" class="guest-input" required>
              </div>
            </div>
          </div>

          <!-- Book Now Button -->
          <button type="submit" class="book-now-btn">
            Book Now
          </button>
        </form>
      </div>

      <!-- Surroundings Section -->
      <div class="surroundings-section">
        <h3 class="surroundings-title">
          <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
          </svg>
          Surroundings
        </h3>

        <div class="surroundings-list">
          <div class="surrounding-item">
            <svg class="surrounding-icon" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
            </svg>
            <span class="surrounding-text">Metro: Ramkhamhaeng</span>
            <span class="surrounding-distance">(1.9 km)</span>
          </div>

          <div class="surrounding-item">
            <svg class="surrounding-icon" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
            </svg>
            <span class="surrounding-text">Metro: Thong Lo</span>
            <span class="surrounding-distance">(2.8 km)</span>
          </div>

          <div class="surrounding-item">
            <svg class="surrounding-icon" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 2L3 7v11a2 2 0 002 2h4v-6h2v6h4a2 2 0 002-2V7l-7-5z"/>
            </svg>
            <span class="surrounding-text">Airport: Suvarnabhumi Airport</span>
            <span class="surrounding-distance">(24.9 km)</span>
          </div>

          <div class="surrounding-item">
            <svg class="surrounding-icon" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 2L3 7v11a2 2 0 002 2h4v-6h2v6h4a2 2 0 002-2V7l-7-5z"/>
            </svg>
            <span class="surrounding-text">Airport: Don Mueang International Airport</span>
            <span class="surrounding-distance">(25.0 km)</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Enhanced Image Modal -->
<div id="imageModal" class="image-modal" onclick="closeImageModal(event)">
  <div class="image-modal-content" onclick="event.stopPropagation()">
    <div class="modal-header">
      <div class="modal-counter">
        <span id="currentImageIndex">1</span> / <span id="totalImages">4</span>
      </div>
      <button class="image-modal-close" onclick="closeImageModal()">&times;</button>
    </div>

    <div class="modal-image-container">
      <button class="modal-nav-btn modal-prev" onclick="previousImage()" id="prevBtn">
        <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
        </svg>
      </button>

      <img id="modalImage" src="" alt="" class="modal-image">

      <button class="modal-nav-btn modal-next" onclick="nextImage()" id="nextBtn">
        <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
        </svg>
      </button>
    </div>

    <div class="modal-thumbnails" id="modalThumbnails">
      <!-- Dynamic thumbnails will be added here -->
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {


  // Removed tab functionality - only Overview tab remains

  // Booking form functionality - Hotel Style
  setTimeout(function() {
    const basePrice = {{ product.selected_or_first_available_variant.price | divided_by: 100.0 }};
    const adultsInput = document.getElementById('adults');
    const childrenInput = document.getElementById('children');
    const totalPriceDisplay = document.getElementById('totalPrice');
    const guestNamesContainer = document.getElementById('guestNames');

    console.log('Hotel booking form initializing...', {
      adultsInput, childrenInput, totalPriceDisplay, guestNamesContainer
    });

    if (adultsInput && childrenInput && totalPriceDisplay && guestNamesContainer) {

      function updatePrice() {
        const adults = parseInt(adultsInput.value) || 2;
        const children = parseInt(childrenInput.value) || 0;
        const totalPrice = (adults * basePrice) + (children * basePrice);
        const priceText = totalPrice.toFixed(2) + ' GEL';
        totalPriceDisplay.textContent = priceText;

        console.log('Price updated:', totalPrice);
      }

      function updateGuestNames() {
        const adults = parseInt(adultsInput.value) || 2;
        const children = parseInt(childrenInput.value) || 0;
        const totalGuests = adults + children;

        guestNamesContainer.innerHTML = '';

        for (let i = 0; i < totalGuests; i++) {
          const guestRow = document.createElement('div');
          guestRow.className = 'guest-name-row';
          guestRow.innerHTML = `
            <select class="title-select">
              <option>Mr</option>
              <option>Ms</option>
              <option>Mrs</option>
            </select>
            <input type="text" placeholder="Guest name" class="guest-input" required>
          `;
          guestNamesContainer.appendChild(guestRow);
        }
        console.log('Guest names updated for', totalGuests, 'guests');
      }

      // Quantity buttons
      document.addEventListener('click', function(e) {
        if (e.target.classList.contains('hotel-qty-btn')) {
          e.preventDefault();

          const target = e.target.getAttribute('data-target');
          const input = document.getElementById(target);
          const isPlus = e.target.classList.contains('hotel-plus');

          if (!input) return;

          const currentValue = parseInt(input.value) || 0;
          const min = parseInt(input.getAttribute('min')) || 0;
          const max = parseInt(input.getAttribute('max')) || 10;

          let newValue = currentValue;
          if (isPlus && currentValue < max) {
            newValue = currentValue + 1;
          } else if (!isPlus && currentValue > min) {
            newValue = currentValue - 1;
          }

          // Check total people limit (4 max)
          const adults = target === 'adults' ? newValue : parseInt(adultsInput.value) || 0;
          const children = target === 'children' ? newValue : parseInt(childrenInput.value) || 0;

          if (adults + children <= 4) {
            input.value = newValue;
            updatePrice();
            updateGuestNames();
          }

          console.log('Hotel button clicked:', target, newValue);
        }
      });

      // Initialize
      updatePrice();
      updateGuestNames();

      // Form submission
      const bookingForm = document.getElementById('bookingForm');
      if (bookingForm) {
        bookingForm.addEventListener('submit', function(e) {
          e.preventDefault();

          // Collect all guest names
          const guestInputs = document.querySelectorAll('.guest-input');
          const guestNames = [];
          guestInputs.forEach((input, index) => {
            if (input.value.trim()) {
              const titleSelect = input.parentElement.querySelector('.title-select');
              const title = titleSelect ? titleSelect.value : 'Mr';
              guestNames.push(`${title} ${input.value.trim()}`);
            }
          });

          // Pre-fill booking data
          const bookingData = {
            date: document.getElementById('booking-date').value,
            adults: document.getElementById('adults').value,
            children: document.getElementById('children').value,
            totalPrice: document.getElementById('totalPrice').textContent,
            guestNames: guestNames
          };

          // Create detailed booking message (for internal use only)
          const bookingDetails = `TOUR BOOKING REQUEST

Date: ${bookingData.date}
Adults: ${bookingData.adults}
Children: ${bookingData.children}
Total Price: ${bookingData.totalPrice}

Guest Names:
${bookingData.guestNames.map((name, index) => `${index + 1}. ${name}`).join('\n')}

Additional Information:`;

          // Find the main contact form (contact-form section)
          const mainContactForm = document.querySelector('#ContactForm') ||
                                 document.querySelector('form[action*="contact"]') ||
                                 document.querySelector('.contact-form');

          if (mainContactForm) {
            // Add booking details as hidden fields to the main contact form
            const existingBookingField = mainContactForm.querySelector('input[name="contact[booking_details]"]');
            if (!existingBookingField) {
              const hiddenField = document.createElement('input');
              hiddenField.type = 'hidden';
              hiddenField.name = 'contact[booking_details]';
              hiddenField.value = bookingDetails;
              mainContactForm.appendChild(hiddenField);
            } else {
              existingBookingField.value = bookingDetails;
            }

            // Add product info as hidden fields
            const productField = document.createElement('input');
            productField.type = 'hidden';
            productField.name = 'contact[product_title]';
            productField.value = '{{ product.title }}';
            mainContactForm.appendChild(productField);

            const sourceField = document.createElement('input');
            sourceField.type = 'hidden';
            sourceField.name = 'contact[source_page]';
            sourceField.value = '{{ product.handle }}';
            mainContactForm.appendChild(sourceField);

            // Scroll to main contact form
            mainContactForm.scrollIntoView({ behavior: 'smooth', block: 'start' });

            // Focus on first input after scrolling
            setTimeout(() => {
              const firstInput = mainContactForm.querySelector('input[type="text"]') ||
                               mainContactForm.querySelector('input[name*="name"]');
              if (firstInput) {
                firstInput.focus();
              }
            }, 1000);

          } else {
            // Fallback: scroll to bottom of page where contact form might be
            window.scrollTo({
              top: document.body.scrollHeight,
              behavior: 'smooth'
            });
          }
        });
      }

    }
  }, 500);
});

// Enhanced Image Modal Functions
let currentImageIndex = 0;
let imageGallery = [];

// Initialize image gallery with ALL product images
function initImageGallery() {
  imageGallery = [];

  // Get all product images from Liquid data
  const allProductImages = [
    {% for media in product.media %}
      {% if media.media_type == 'image' %}
        {
          src: "{{ media | image_url: width: 1200 }}",
          thumb: "{{ media | image_url: width: 300 }}",
          alt: "{{ media.alt | escape | default: 'Product Image' }}"
        }{% unless forloop.last %},{% endunless %}
      {% endif %}
    {% endfor %}
  ];

  // Add all images to gallery
  imageGallery = allProductImages;

  updateTotalImages();
}

function openImageModal(imageSrc, imageIndex = 0) {
  if (imageGallery.length === 0) {
    initImageGallery();
  }

  // Find the index of the clicked image
  if (typeof imageIndex === 'string') {
    const foundIndex = imageGallery.findIndex(img => img.src === imageSrc);
    currentImageIndex = foundIndex !== -1 ? foundIndex : 0;
  } else {
    currentImageIndex = imageIndex;
  }

  const modal = document.getElementById('imageModal');
  if (modal) {
    updateModalImage();
    createThumbnails();
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';

    // Add keyboard navigation
    document.addEventListener('keydown', handleKeyNavigation);
  }
}

function closeImageModal(event) {
  if (event && event.target !== event.currentTarget) return;

  const modal = document.getElementById('imageModal');
  if (modal) {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
    document.removeEventListener('keydown', handleKeyNavigation);
  }
}

function updateModalImage() {
  const modalImage = document.getElementById('modalImage');
  const currentIndexSpan = document.getElementById('currentImageIndex');

  if (modalImage && imageGallery[currentImageIndex]) {
    modalImage.src = imageGallery[currentImageIndex].src;
    modalImage.alt = imageGallery[currentImageIndex].alt;

    if (currentIndexSpan) {
      currentIndexSpan.textContent = currentImageIndex + 1;
    }

    // Update navigation buttons
    updateNavigationButtons();
    updateActiveThumbnail();
  }
}

function updateNavigationButtons() {
  const prevBtn = document.getElementById('prevBtn');
  const nextBtn = document.getElementById('nextBtn');

  if (prevBtn) {
    prevBtn.style.display = currentImageIndex > 0 ? 'flex' : 'none';
  }

  if (nextBtn) {
    nextBtn.style.display = currentImageIndex < imageGallery.length - 1 ? 'flex' : 'none';
  }
}

function updateTotalImages() {
  const totalImagesSpan = document.getElementById('totalImages');
  if (totalImagesSpan) {
    totalImagesSpan.textContent = imageGallery.length;
  }
}

function createThumbnails() {
  const thumbnailsContainer = document.getElementById('modalThumbnails');
  if (!thumbnailsContainer) return;

  thumbnailsContainer.innerHTML = '';

  imageGallery.forEach((image, index) => {
    const thumbDiv = document.createElement('div');
    thumbDiv.className = `modal-thumb ${index === currentImageIndex ? 'active' : ''}`;
    thumbDiv.onclick = () => goToImage(index);

    const thumbImg = document.createElement('img');
    thumbImg.src = image.thumb;
    thumbImg.alt = image.alt;

    thumbDiv.appendChild(thumbImg);
    thumbnailsContainer.appendChild(thumbDiv);
  });
}

function updateActiveThumbnail() {
  const thumbs = document.querySelectorAll('.modal-thumb');
  thumbs.forEach((thumb, index) => {
    thumb.classList.toggle('active', index === currentImageIndex);
  });
}

function previousImage() {
  if (currentImageIndex > 0) {
    currentImageIndex--;
    updateModalImage();
  }
}

function nextImage() {
  if (currentImageIndex < imageGallery.length - 1) {
    currentImageIndex++;
    updateModalImage();
  }
}

function goToImage(index) {
  currentImageIndex = index;
  updateModalImage();
}

function handleKeyNavigation(event) {
  switch(event.key) {
    case 'Escape':
      closeImageModal();
      break;
    case 'ArrowLeft':
      previousImage();
      break;
    case 'ArrowRight':
      nextImage();
      break;
  }
}

// Initialize gallery when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  setTimeout(() => {
    initImageGallery();
  }, 1000);
});
</script>

<style>
/* Removed contact form styling - using main contact form instead */
</style>
